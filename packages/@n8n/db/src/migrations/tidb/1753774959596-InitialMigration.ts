import type { MigrationContext, ReversibleMigration } from '../migration-types';

export class InitialMigration1753774959596 implements ReversibleMigration {
	async up({ queryRunner, tablePrefix }: MigrationContext) {
		await queryRunner.query(
			'CREATE TABLE IF NOT EXISTS `' +
				'galaxy_permission` (`id` int NOT NULL AUTO_INCREMENT, `res_id` varchar(255) NOT NULL, `res_type` varchar(64) NOT NULL, `project_id` varchar(255) NOT NULL, INDEX ' +
				'`galaxy_permission_project_id_IDX` (`project_id`, `res_type`), PRIMARY KEY (`id`)) ENGINE=InnoDB',
		);
	}

	async down({ queryRunner, tablePrefix }: MigrationContext) {
		await queryRunner.query(
			'DROP INDEX ' + 'galaxy_permission_project_id_IDX` ON `' + 'galaxy_permission`',
		);
		await queryRunner.query('DROP TABLE `' + 'galaxy_permission`');
	}
}
