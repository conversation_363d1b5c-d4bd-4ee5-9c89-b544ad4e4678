import { GlobalConfig } from '@n8n/config';
import { Service } from '@n8n/di';
import { DataSource, Repository } from '@n8n/typeorm';
import { GalaxyPermission } from '../entities';

@Service()
export class GalaxyPermissionRepository extends Repository<GalaxyPermission> {
	constructor(
		dataSource: DataSource,
		private readonly globalConfig: GlobalConfig,
	) {
		super(GalaxyPermission, dataSource.manager);
	}

	async create(gPermit: GalaxyPermission) {
		return await this.save(gPermit);
	}
}
