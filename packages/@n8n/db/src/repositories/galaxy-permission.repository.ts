import { Service } from '@n8n/di';
import { DataSource, Repository } from '@n8n/typeorm';

import { GalaxyPermission } from '../entities';

@Service()
export class GalaxyPermissionRepository extends Repository<GalaxyPermission> {
	constructor(dataSource: DataSource) {
		super(GalaxyPermission, dataSource.manager);
	}

	async addGalaxyPermitRecord(gPermit: GalaxyPermission) {
		return await this.save(gPermit);
	}
}
