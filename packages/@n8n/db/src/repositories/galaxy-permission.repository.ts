import { GlobalConfig } from '@n8n/config';
import { Service } from '@n8n/di';
import { DataSource, Repository } from '@n8n/typeorm';
import { GalaxyPermissionEntity } from '../entities';

@Service()
export class GalaxyPermissionRepository extends Repository<GalaxyPermissionEntity> {
	constructor(
		dataSource: DataSource,
		private readonly globalConfig: GlobalConfig,
	) {
		super(GalaxyPermissionEntity, dataSource.manager);
	}

	// async get(
	// 	where: FindOptionsWhere<WorkflowEntity>,
	// 	options?: { relations: string[] | FindOptionsRelations<WorkflowEntity> },
	// ) {
	// 	return await this.findOne({
	// 		where,
	// 		relations: options?.relations,
	// 	});
	// }

	async create(gPermit: GalaxyPermissionEntity) {
		return await this.save(gPermit);
	}
}
